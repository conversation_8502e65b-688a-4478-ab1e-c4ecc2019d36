# OpenAI GPT-5 Nano Backup Model Setup

## Environment Variables

Add the following environment variable to your `.env` file:

```env
OPENAI_API_KEY=your_openai_api_key_here
```

## How It Works

The system now includes automatic fallback from Google Gemini to OpenAI GPT-5 Nano:

1. **Primary Model**: Google Gemini (first 2 attempts)
2. **Backup Model**: OpenAI GPT-5 Nano (final attempt)

## Features Added

### Enhanced Error Handling
- Comprehensive retry logic with exponential backoff
- Support for both Gemini and OpenAI specific error codes
- Automatic model switching on failure
- **Empty Response Detection**: Automatically switches to backup if Gemini returns empty text

### Model Usage Tracking
- Each response now includes `modelUsed` field
- Logs which model was used for each request
- Helps monitor system reliability

### Smart Retry Strategy
- First 2 attempts: Google Gemini 2.5 Flash
- Final attempt: OpenAI GPT-5 Nano
- Exponential backoff between retries
- Respects retryable vs non-retryable errors
- **Intelligent Fallback**: If <PERSON> succeeds but returns empty text, immediately switches to GPT-5 Nano

## Updated Files

1. **app/api/chat/route.ts**
   - Added OpenAI import
   - Enhanced error handling
   - Backup model support
   - Model usage tracking in response

2. **app/api/human/route.ts**
   - Added OpenAI import
   - Enhanced error handling
   - Backup model support
   - Model usage tracking in response

3. **package.json**
   - Added @ai-sdk/openai dependency
   - Added @ai-sdk/google dependency
   - Added ai dependency
   - Added zod dependency
   - Added redis dependency
   - Added jsdom dependency
   - Added @types/jsdom dev dependency

## Benefits

1. **High Availability**: Automatic fallback ensures service continuity
2. **Error Resilience**: Smart retry logic handles temporary failures
3. **Empty Response Protection**: Automatically detects and handles empty Gemini responses
4. **Monitoring**: Track which model is being used
5. **Performance**: Exponential backoff prevents overwhelming APIs
6. **Flexibility**: Easy to add more backup models in the future
7. **Intelligent Switching**: No wasted attempts - immediately switches on empty responses

## Response Format

Responses now include model information:

```json
{
  "response": "Generated response text",
  "modelUsed": "gemini-2.5-flash" | "gpt-5-nano" | "unknown",
  "timestamp": "2025-01-18 15:30:45",
  "sanitization_applied": false,
  "emoji_filtered": false,
  "original_length": 100,
  "sanitized_length": 100,
  "final_length": 95,
  "tool_calls": [],
  "tool_results": [],
  "tool_usage": {},
  "tools_used_count": 0
}
```

## Installation

Run the following command to install the new dependencies:

```bash
npm install @ai-sdk/openai @ai-sdk/google ai zod redis jsdom @types/jsdom
```

## Enhanced Fallback Logic

The system now includes intelligent detection of empty responses:

### Scenario 1: Gemini API Error
```
Attempt 1: Gemini fails with error → Retry with Gemini
Attempt 2: Gemini fails with error → Switch to GPT-5 Nano
Attempt 3: GPT-5 Nano succeeds → Return response
```

### Scenario 2: Gemini Returns Empty Response
```
Attempt 1: Gemini succeeds but returns empty text → Immediately switch to GPT-5 Nano
Attempt 2: GPT-5 Nano succeeds → Return response
```

### Scenario 3: Both Models Fail
```
Attempt 1: Gemini fails → Retry with Gemini
Attempt 2: Gemini fails → Switch to GPT-5 Nano
Attempt 3: GPT-5 Nano fails → Return error
```

This ensures that users always get a meaningful response, even if Gemini has issues generating text.

## Configuration Notes

- Ensure your OpenAI API key has access to GPT-5 Nano model
- The system will gracefully degrade if OpenAI API is not configured
- Monitor logs to see which model is being used for requests
- Consider rate limits for both Google and OpenAI APIs
- Watch for "⚠️ Gemini returned empty response" logs to monitor empty response frequency
